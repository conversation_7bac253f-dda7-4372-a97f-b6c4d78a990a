import 'package:cloud_firestore/cloud_firestore.dart';
import '../constants/app_constants.dart';
import '../models/user_model.dart';
import '../models/product_model.dart';
import '../models/notification_model.dart';
import '../enums/notification_type.dart';
import 'user_management_service.dart';
import 'product_service.dart';

class AdminNotificationDataService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get aggregated notification data for admin dashboard
  static Stream<List<NotificationModel>> getAggregatedNotificationsStream() {
    return Stream.periodic(const Duration(seconds: 30), (i) => i)
        .asyncMap((_) => _generateNotifications())
        .distinct();
  }

  // Generate notifications based on pending data
  static Future<List<NotificationModel>> _generateNotifications() async {
    List<NotificationModel> notifications = [];

    try {
      // Get pending registrations
      final pendingRegistrations = await UserManagementService.getPendingRegistrationUsers();
      if (pendingRegistrations.isNotEmpty) {
        notifications.add(NotificationModel(
          id: 'pending_registrations_${DateTime.now().millisecondsSinceEpoch}',
          userId: 'admin',
          fromUserId: 'system',
          fromUserName: 'System',
          fromUserAvatar: '',
          type: NotificationType.userRegistration,
          title: 'New User Registrations',
          message: '${pendingRegistrations.length} new user registration${pendingRegistrations.length > 1 ? 's' : ''} pending approval',
          relatedId: 'pending-registrations',
          data: {
            'count': pendingRegistrations.length,
            'type': 'pendingRegistrations',
            'users': pendingRegistrations.map((u) => {
              'id': u.id,
              'name': u.displayNameOrUsername,
              'email': u.email,
              'createdAt': u.createdAt?.toIso8601String(),
            }).toList(),
          },
          isRead: false,
          createdAt: DateTime.now(),
        ));
      }

      // Get pending reseller applications
      final pendingResellerApplications = await UserManagementService.getPendingResellerApplications();
      if (pendingResellerApplications.isNotEmpty) {
        notifications.add(NotificationModel(
          id: 'pending_reseller_apps_${DateTime.now().millisecondsSinceEpoch}',
          userId: 'admin',
          fromUserId: 'system',
          fromUserName: 'System',
          fromUserAvatar: '',
          type: NotificationType.resellerApplication,
          title: 'Reseller Applications',
          message: '${pendingResellerApplications.length} reseller application${pendingResellerApplications.length > 1 ? 's' : ''} pending review',
          relatedId: 'reseller-applications',
          data: {
            'count': pendingResellerApplications.length,
            'type': 'pendingResellerApplications',
            'applications': pendingResellerApplications.map((u) => {
              'id': u.id,
              'name': u.displayNameOrUsername,
              'email': u.email,
              'applicationDate': u.resellerApplicationDate?.toIso8601String(),
            }).toList(),
          },
          isRead: false,
          createdAt: DateTime.now(),
        ));
      }

      // Get pending products
      final pendingProducts = await _getPendingProducts();
      if (pendingProducts.isNotEmpty) {
        notifications.add(NotificationModel(
          id: 'pending_products_${DateTime.now().millisecondsSinceEpoch}',
          userId: 'admin',
          fromUserId: 'system',
          fromUserName: 'System',
          fromUserAvatar: '',
          type: NotificationType.system,
          title: 'Pending Products',
          message: '${pendingProducts.length} product${pendingProducts.length > 1 ? 's' : ''} pending approval',
          relatedId: 'pending-products',
          data: {
            'count': pendingProducts.length,
            'type': 'pendingProducts',
            'products': pendingProducts.map((p) => {
              'id': p.id,
              'name': p.name,
              'sellerName': p.sellerName,
              'createdAt': p.createdAt?.toIso8601String(),
            }).toList(),
          },
          isRead: false,
          createdAt: DateTime.now(),
        ));
      }

    } catch (e) {
      print('Error generating notifications: $e');
    }

    return notifications;
  }

  // Get pending products
  static Future<List<ProductModel>> _getPendingProducts() async {
    try {
      final QuerySnapshot querySnapshot = await _firestore
          .collection(AppConstants.productsCollection)
          .where('status', isEqualTo: 'pending')
          .orderBy('createdAt', descending: true)
          .limit(50) // Limit to avoid too many results
          .get();

      return querySnapshot.docs
          .map((doc) => ProductModel.fromDocument(doc))
          .toList();
    } catch (e) {
      print('Error fetching pending products: $e');
      return [];
    }
  }

  // Get notification counts for dashboard
  static Future<Map<String, int>> getNotificationCounts() async {
    try {
      final pendingRegistrations = await UserManagementService.getPendingRegistrationUsers();
      final pendingResellerApplications = await UserManagementService.getPendingResellerApplications();
      final pendingProducts = await _getPendingProducts();

      return {
        'pendingRegistrations': pendingRegistrations.length,
        'pendingResellerApplications': pendingResellerApplications.length,
        'pendingProducts': pendingProducts.length,
        'total': pendingRegistrations.length + pendingResellerApplications.length + pendingProducts.length,
      };
    } catch (e) {
      print('Error getting notification counts: $e');
      return {
        'pendingRegistrations': 0,
        'pendingResellerApplications': 0,
        'pendingProducts': 0,
        'total': 0,
      };
    }
  }

  // Mark notification as read (for future use)
  static Future<void> markNotificationAsRead(String notificationId) async {
    try {
      // This would be implemented if we store these notifications in Firestore
      // For now, these are generated notifications, so we don't need to mark them as read
      print('Marking notification $notificationId as read');
    } catch (e) {
      print('Error marking notification as read: $e');
    }
  }

  // Get real-time counts stream
  static Stream<Map<String, int>> getNotificationCountsStream() {
    return Stream.periodic(const Duration(seconds: 30), (i) => i)
        .asyncMap((_) => getNotificationCounts())
        .distinct();
  }
}
