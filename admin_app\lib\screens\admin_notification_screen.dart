import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/notification_model.dart';
import '../services/notification_service.dart';
import '../services/admin_notification_data_service.dart';
import '../providers/auth_provider.dart';
import '../widgets/common/custom_app_bar.dart';
import '../widgets/common/swipe_refresh_mixin.dart';
import '../enums/notification_type.dart';

class AdminNotificationScreen extends StatefulWidget {
  const AdminNotificationScreen({super.key});

  @override
  State<AdminNotificationScreen> createState() => _AdminNotificationScreenState();
}

class _AdminNotificationScreenState extends State<AdminNotificationScreen> with SwipeRefreshMixin {

  @override
  Future<void> onRefresh() async {
    // Refresh notifications by rebuilding the stream
    setState(() {});
  }

  // Combine regular notifications with admin-specific notifications
  Stream<List<NotificationModel>> _getCombinedNotificationsStream() {
    return Stream.periodic(const Duration(seconds: 5), (i) => i)
        .asyncMap((_) async {
          try {
            // Get regular admin notifications
            final regularNotifications = await NotificationService.getAdminNotificationsStream().first;

            // Get admin-specific notifications (pending items)
            final adminNotifications = await AdminNotificationDataService.getAggregatedNotificationsStream().first;

            // Combine and sort by creation time
            final allNotifications = [...regularNotifications, ...adminNotifications];
            allNotifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));

            return allNotifications;
          } catch (e) {
            print('Error getting combined notifications: $e');
            return <NotificationModel>[];
          }
        });
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final currentUser = authProvider.currentUser;

    if (currentUser == null) {
      return const Scaffold(
        body: Center(
          child: Text('Please log in to view notifications'),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: const CustomAppBar(
        title: 'Admin Notifications',
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
      ),
      body: StreamBuilder<List<NotificationModel>>(
        stream: _getCombinedNotificationsStream(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading notifications',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Please try again later',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            );
          }

          final notifications = snapshot.data ?? [];

          if (notifications.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.notifications_none,
                    size: 64,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No notifications yet',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Admin notifications will appear here',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            );
          }

          return buildRefreshableListView(
            itemCount: notifications.length,
            itemBuilder: (context, index) {
              final notification = notifications[index];
              return _buildNotificationCard(notification);
            },
            padding: const EdgeInsets.all(16),
          );
        },
      ),
    );
  }

  Widget _buildNotificationCard(NotificationModel notification) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: notification.isRead ? 1 : 3,
      color: notification.isRead 
          ? Theme.of(context).colorScheme.surface
          : Theme.of(context).colorScheme.primary.withOpacity(0.05),
      child: ListTile(
        onTap: () => _handleNotificationTap(notification),
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          radius: 24,
          backgroundColor: _getNotificationColor(notification.type).withOpacity(0.1),
          child: Icon(
            _getNotificationIcon(notification.type),
            color: _getNotificationColor(notification.type),
            size: 20,
          ),
        ),
        title: Text(
          notification.title,
          style: TextStyle(
            fontWeight: notification.isRead ? FontWeight.w500 : FontWeight.w600,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              notification.message,
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                fontSize: 14,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getNotificationColor(notification.type).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    notification.typeDisplayName,
                    style: TextStyle(
                      color: _getNotificationColor(notification.type),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  notification.timeAgo,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: !notification.isRead
            ? Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  shape: BoxShape.circle,
                ),
              )
            : null,
      ),
    );
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.userRegistration:
        return Icons.person_add;
      case NotificationType.resellerApplication:
        return Icons.business;
      case NotificationType.postReported:
        return Icons.report;
      case NotificationType.userVerification:
        return Icons.verified_user;
      case NotificationType.system:
        // Check if it's a pending product notification
        return Icons.inventory;
      default:
        return Icons.notifications;
    }
  }

  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.userRegistration:
        return Colors.green;
      case NotificationType.resellerApplication:
        return Colors.blue;
      case NotificationType.postReported:
        return Colors.red;
      case NotificationType.userVerification:
        return Colors.orange;
      case NotificationType.system:
        return Colors.purple;
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }

  void _handleNotificationTap(NotificationModel notification) async {
    // Mark as read
    if (!notification.isRead) {
      await NotificationService.markAsRead(notification.id);
    }

    // Navigate based on notification type
    switch (notification.type) {
      case NotificationType.userRegistration:
        Navigator.pushNamed(context, '/pending-registrations');
        break;
      case NotificationType.resellerApplication:
        Navigator.pushNamed(context, '/reseller-applications');
        break;
      case NotificationType.postReported:
        Navigator.pushNamed(context, '/moderation');
        break;
      case NotificationType.userVerification:
        Navigator.pushNamed(context, '/users');
        break;
      case NotificationType.system:
        // Check if it's a pending product notification
        if (notification.data?['type'] == 'pendingProduct') {
          Navigator.pushNamed(context, '/pending-products');
        } else {
          Navigator.pushNamed(context, '/settings');
        }
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Notification: ${notification.title}')),
        );
        break;
    }
  }

  Future<void> _markAllAsRead() async {
    try {
      // Get all admin notifications and mark them as read
      final notifications = await NotificationService.getAdminNotifications();
      for (final notification in notifications) {
        if (!notification.isRead) {
          await NotificationService.markAsRead(notification.id);
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('All notifications marked as read')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error marking notifications as read: $e')),
        );
      }
    }
  }

  Future<void> _createTestNotification() async {
    try {
      final success = await NotificationService.createTestAdminNotification();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success
                ? 'Test notification created successfully'
                : 'Failed to create test notification'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating test notification: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
